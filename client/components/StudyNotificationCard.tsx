import { Card, CardContent } from "./ui/card";
import SuccessCheckIcon from "./icons/SuccessCheckIcon";
import ChevronRightIcon from "./icons/ChevronRightIcon";

interface StudyNotificationCardProps {
  studyProperty: {
    name: string;
    image: string;
  };
}

export function StudyNotificationCard({ studyProperty }: StudyNotificationCardProps) {
  return (
    <Card className="bg-[#DFEDF5] border-none rounded-xl mx-4">
      <CardContent className="p-4 space-y-4">
        {/* Success Icon and Message */}
        <div className="flex flex-col items-center space-y-4">
          <SuccessCheckIcon width={29} height={29} color="#3E7998" />
          <p className="text-center text-sm font-bold text-primary-900 leading-4">
            Não há nenhuma pendência, que tal estudar o último lançamento?
          </p>
        </div>

        {/* Divider */}
        <div className="border-t border-surface-200" />

        {/* Property Info */}
        <div className="flex items-center gap-4">
          <img 
            src={studyProperty.image}
            alt={studyProperty.name}
            className="w-[84px] h-20 rounded-xl object-cover"
          />
          <div className="flex-1">
            <p className="text-sm font-medium text-primary-900 leading-4">
              {studyProperty.name}
            </p>
          </div>
          <ChevronRightIcon width={8} height={14} color="#3E7998" />
        </div>
      </CardContent>
    </Card>
  );
}