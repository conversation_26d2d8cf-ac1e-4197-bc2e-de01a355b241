export function PerformanceChart() {
  const chartData = [
    { day: 'SEG', date: '7/8', value: 0, height: 1 },
    { day: 'TER', date: '8/8', value: 7, height: 28 },
    { day: 'QUA', date: '9/8', value: 21, height: 124 },
    { day: 'QUI', date: '10/8', value: 27, height: 149 },
    { day: 'SEX', date: '11/8', value: 13, height: 83 },
    { day: 'SÁB', date: '12/8', value: 24, height: 142 },
    { day: 'DOM', date: '13/8', value: 10, height: 59 },
  ];

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="px-4 pt-6 pb-4">
        <h2 className="text-lg font-bold text-primary-900 mb-4">
          Atendimentos por período
        </h2>
        
        {/* Period Selector */}
        <div className="flex items-center justify-between px-4 py-2 bg-primary-50 rounded-xl">
          <div className="flex items-center gap-2">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="w-4 h-4">
              <path fillRule="evenodd" clipRule="evenodd" d="M12.6667 15.3333C13.7667 15.3333 14.6667 14.4333 14.6667 13.3333V3.99996C14.6667 2.89996 13.7667 1.99996 12.6667 1.99996H11.3333V1.33329C11.3333 0.966626 11.0333 0.666626 10.6667 0.666626C10.3 0.666626 10 0.966626 10 1.33329V1.99996H6V1.33329C6 0.966626 5.7 0.666626 5.33333 0.666626C4.96667 0.666626 4.66667 0.966626 4.66667 1.33329V1.99996H3.33333C2.23333 1.99996 1.33333 2.89996 1.33333 3.99996V13.3333C1.33333 14.4333 2.23333 15.3333 3.33333 15.3333H12.6667ZM12.6667 14C13.0333 14 13.3333 13.7 13.3333 13.3333V7.33329H2.66667V13.3333C2.66667 13.7 2.96667 14 3.33333 14H12.6667ZM12.6667 3.33329C13.0333 3.33329 13.3333 3.63329 13.3333 3.99996V5.99996H2.66667V3.99996C2.66667 3.63329 2.96667 3.33329 3.33333 3.33329H4.66667V3.99996C4.66667 4.36663 4.96667 4.66663 5.33333 4.66663C5.7 4.66663 6 4.36663 6 3.99996V3.33329H10V3.99996C10 4.36663 10.3 4.66663 10.6667 4.66663C11.0333 4.66663 11.3333 4.36663 11.3333 3.99996V3.33329H12.6667Z" fill="#203C47"/>
            </svg>
            <span className="text-sm font-bold text-baseline-primary">
              Últimos 7 dias
            </span>
          </div>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className="w-5 h-5">
            <path d="M10.0021 13.3271C9.7854 13.3271 9.57707 13.2437 9.41041 13.0854L4.41041 8.08538C4.08541 7.76038 4.08541 7.23538 4.41041 6.91038C4.73541 6.58538 5.26041 6.58538 5.58541 6.91038L9.99374 11.3187L14.4021 6.91038C14.7271 6.58538 15.2521 6.58538 15.5771 6.91038C15.9021 7.23538 15.9021 7.76038 15.5771 8.08538L10.5771 13.0854C10.4104 13.252 10.2021 13.3271 9.98541 13.3271H10.0021Z" fill="#203C47"/>
          </svg>
        </div>
      </div>

      {/* Chart */}
      <div className="px-4">
        <div className="p-4 bg-white rounded-xl shadow-sm">
          <div className="h-80 relative">
            {/* Y-axis labels */}
            <div className="absolute left-0 top-0 h-64 flex flex-col justify-between text-right pr-3">
              <span className="text-xs text-gray-500">40</span>
              <span className="text-xs text-gray-500">30</span>
              <span className="text-xs text-gray-500">20</span>
              <span className="text-xs text-gray-500">10</span>
              <span className="text-xs text-gray-500">0</span>
            </div>

            {/* Grid lines */}
            <div className="absolute left-12 top-0 right-0 h-64 flex flex-col justify-between">
              <div className="h-px bg-gray-300"></div>
              <div className="h-px bg-gray-300"></div>
              <div className="h-px bg-gray-300"></div>
              <div className="h-px bg-gray-300"></div>
              <div className="h-px bg-gray-300"></div>
            </div>

            {/* Chart bars */}
            <div className="absolute left-12 top-0 right-0 h-64 flex items-end justify-between px-4">
              {chartData.map((item, index) => (
                <div key={index} className="flex flex-col items-center gap-1">
                  <span className="text-sm font-bold text-gray-500 mb-1">
                    {item.value}
                  </span>
                  <div 
                    className="w-5 bg-baseline-secondary rounded-t"
                    style={{ height: `${Math.max(item.height * 0.4, 4)}px` }}
                  ></div>
                </div>
              ))}
            </div>

            {/* X-axis labels */}
            <div className="absolute left-12 bottom-0 right-0 border-t border-gray-300">
              <div className="flex justify-between px-4 pt-2">
                {chartData.map((item, index) => (
                  <div key={index} className="flex flex-col items-center gap-1">
                    <div className="w-px h-1.5 bg-gray-300"></div>
                    <div className="text-center">
                      <div className="text-xs text-gray-500">{item.date}</div>
                      <div className="text-xs font-bold text-baseline-primary">{item.day}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
