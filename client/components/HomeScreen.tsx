import { StatusBar } from "./StatusBar";
import { CheckInHeader } from "./CheckInHeader";
import { StudyNotificationCard } from "./StudyNotificationCard";
import { SectionHeader } from "./SectionHeader";
import { EmptyStateCard } from "./EmptyStateCard";
import { BottomNavigation } from "./BottomNavigation";
import CalendarIcon from "./icons/CalendarIcon";
import LocationIcon from "./icons/LocationIcon";
import { HomeScreenProps } from "./HomeScreenMockData";

export function HomeScreen({
  userName,
  currentTime,
  hasNotifications,
  studyProperty,
  hasUpcomingAppointments,
  hasNearbyDutyShifts,
  locationPermissionGranted
}: HomeScreenProps) {
  return (
    <div className="min-h-screen bg-white flex flex-col max-w-sm mx-auto relative">
      {/* Status Bar */}
      <StatusBar time={currentTime} />
      
      {/* Check-in Header */}
      <CheckInHeader hasNotifications={hasNotifications} />
      
      {/* Main Content */}
      <div className="flex-1 pb-24 space-y-6">
        {/* Greeting */}
        <div className="px-4">
          <h1 className="text-lg font-bold text-primary-900 tracking-tight">
            Olá, {userName}
          </h1>
        </div>
        
        {/* Study Notification Card */}
        <StudyNotificationCard studyProperty={studyProperty} />
        
        {/* Appointments Section */}
        <div>
          <SectionHeader 
            title="Prróximos atendimentos" 
            actionText="Ver agenda"
            onActionClick={() => console.log('Ver agenda clicked')}
          />
          
          {!hasUpcomingAppointments && (
            <EmptyStateCard
              icon={<CalendarIcon width={42} height={44} color="#4F95B0" />}
              title="Não há atendimentos"
              message="[Mensagem]"
              buttonText="Agendar atendimento"
              onButtonClick={() => console.log('Agendar atendimento clicked')}
            />
          )}
        </div>
        
        {/* Duty Shifts Section */}
        <div>
          <SectionHeader 
            title="Plantões próximos" 
            actionText="Ver todos"
            onActionClick={() => console.log('Ver todos clicked')}
          />
          
          {!hasNearbyDutyShifts && (
            <EmptyStateCard
              icon={<LocationIcon width={44} height={44} color="#4F95B0" />}
              title="Habilite a localização de seu celular"
              message="[Mensagem]"
              buttonText="Habilitar localização"
              onButtonClick={() => console.log('Habilitar localização clicked')}
            />
          )}
        </div>
      </div>
      
      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
}