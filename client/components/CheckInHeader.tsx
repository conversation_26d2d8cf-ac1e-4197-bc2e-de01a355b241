import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import CheckInIcon from "./icons/CheckInIcon";
import NotificationGroupIcon from "./icons/NotificationGroupIcon";

interface CheckInHeaderProps {
  hasNotifications?: boolean;
}

export function CheckInHeader({ hasNotifications = false }: CheckInHeaderProps) {
  return (
    <div className="flex items-center justify-between px-4 py-3">
      {/* Check-in Button */}
      <Button 
        className="bg-primary-600 hover:bg-primary-600/90 text-white font-bold text-base px-6 py-3 rounded-full flex items-center gap-2"
        size="lg"
      >
        <CheckInIcon width={22} height={20} color="white" />
        Check-in
      </Button>

      {/* Notification Group */}
      <div className="relative">
        <NotificationGroupIcon width={81} height={44} color="#3E7998" />
        {hasNotifications && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-3 w-3 p-0 text-xs"
          />
        )}
      </div>
    </div>
  );
}