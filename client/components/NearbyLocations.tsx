interface LocationCardProps {
  title: string;
  address: string;
  distance: string;
  image: string;
  badge?: string;
}

function LocationCard({ title, address, distance, image, badge }: LocationCardProps) {
  return (
    <div className="space-y-4">
      {/* Location Image */}
      <div
        className="h-[137px] rounded-xl bg-cover bg-center relative"
        style={{ backgroundImage: `url(${image})` }}
      >
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
        {badge && (
          <div className="absolute left-3 bottom-3 px-3 py-1 rounded-lg bg-white/90 text-[13px] font-bold text-primary-900">
            {badge}
          </div>
        )}
      </div>

      {/* Location Info */}
      <div className="flex items-center gap-2">
        <div className="flex-1 space-y-0.5">
          <h3 className="text-lg font-bold text-primary-900 leading-tight">
            {title}
          </h3>
          <p className="text-base text-text-primary">
            {address}
          </p>
          <p className="text-sm text-text-primary">
            {distance}
          </p>
        </div>
        <div className="w-9 h-9 bg-primary-50 rounded-full grid place-items-center">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-5 h-5">
            <path d="M9.00252 18.9825C8.74252 18.9825 8.4925 18.8825 8.2925 18.6925C7.9025 18.3025 7.9025 17.6725 8.2925 17.2825L13.5825 11.9925L8.2925 6.7025C7.9025 6.3125 7.9025 5.6825 8.2925 5.2925C8.6825 4.9025 9.31253 4.9025 9.70253 5.2925L15.7025 11.2925C16.0925 11.6825 16.0925 12.3125 15.7025 12.7025L9.70253 18.7025C9.50253 18.9025 9.25251 18.9925 8.99251 18.9925L9.00252 18.9825Z" fill="#203C47"/>
          </svg>
        </div>
      </div>
    </div>
  );
}

export function NearbyLocations() {
  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between px-4 pt-6 pb-4">
        <h2 className="text-lg font-bold text-primary-900">
          Plantões próximos
        </h2>
        <div className="flex items-center gap-2">
          <span className="text-sm font-bold text-baseline-secondary">
            Ver todos
          </span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className="w-5 h-5">
            <path d="M7.50209 15.8187C7.28542 15.8187 7.07707 15.7354 6.91041 15.577C6.58541 15.252 6.58541 14.727 6.91041 14.402L11.3188 9.99372L6.91041 5.58538C6.58541 5.26038 6.58541 4.73538 6.91041 4.41038C7.23541 4.08538 7.76043 4.08538 8.08543 4.41038L13.0854 9.41038C13.4104 9.73538 13.4104 10.2604 13.0854 10.5854L8.08543 15.5854C7.91877 15.752 7.71042 15.8271 7.49375 15.8271L7.50209 15.8187Z" fill="#3E7998"/>
          </svg>
        </div>
      </div>

      {/* Location Cards */}
      <div className="px-4 space-y-4">
        <LocationCard
          title="Cidade Mooca - Venezia"
          address="Rua Icaraí, 322"
          distance="Distância: 12KM"
          image="https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=400&h=150&fit=crop&crop=center"
          badge="Sorteio: 09h E 13h"
        />
        <LocationCard
          title="Cidade Lapa - Água Branca"
          address="Rua Icaraí, 322"
          distance="Distância: 12KM"
          image="https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=400&h=150&fit=crop&crop=center"
        />
      </div>
    </div>
  );
}
