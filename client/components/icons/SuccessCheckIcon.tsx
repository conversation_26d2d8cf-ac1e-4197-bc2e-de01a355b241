import * as React from "react";
import type { SVGProps } from "react";
const Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 29.333 29.333" {...props}><g xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M14.667 29.334C6.574 29.334 0 22.76 0 14.667S6.574 0 14.667 0s14.667 6.574 14.667 14.667-6.574 14.667-14.667 14.667m0-26.667c-6.613 0-12 5.387-12 12s5.387 12 12 12 12-5.387 12-12-5.387-12-12-12" /><path d="M13.334 18.667c-.347 0-.68-.133-.947-.387L9.72 15.614c-.52-.52-.52-1.36 0-1.88s1.36-.52 1.88 0l1.72 1.72 4.387-4.387c.52-.52 1.36-.52 1.88 0s.52 1.36 0 1.88l-5.333 5.333a1.3 1.3 0 0 1-.947.387z" /></g></svg>;
export default Component;