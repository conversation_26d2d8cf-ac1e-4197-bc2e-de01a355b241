import * as React from "react";
import type { SVGProps } from "react";
const Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 81 44" {...props}><g xmlns="http://www.w3.org/2000/svg"><g fill="#203C47"><path d="M20 33c-6.07 0-11-4.93-11-11s4.93-11 11-11 11 4.93 11 11-4.93 11-11 11m0-20c-4.96 0-9 4.04-9 9s4.04 9 9 9 9-4.04 9-9-4.04-9-9-9" /><path d="M20 24c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4m0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2M25 31.66c-.55 0-1-.45-1-1V29c0-.26-.1-.52-.29-.7-.2-.2-.44-.3-.71-.3h-6c-.27 0-.52.1-.7.29-.19.19-.3.45-.3.71v1.66c0 .55-.45 1-1 1s-1-.45-1-1V29c0-.79.32-1.57.88-2.12A2.98 2.98 0 0 1 17 26h6c.81 0 1.56.32 2.13.89.55.54.87 1.32.87 2.11v1.66c0 .55-.45 1-1 1" /></g><circle cx={31} cy={18} r={4} fill="#D83F20" /><g fill="#203C47"><path d="M61.003 33a2.95 2.95 0 0 1-2.58-1.52 1 1 0 1 1 1.75-.97c.08.15.2.27.35.36.29.17.67.17.96 0 .14-.08.26-.21.34-.36a1 1 0 1 1 1.75.97c-.25.46-.63.85-1.08 1.11-.46.27-.97.41-1.5.41zm9-5h-18c-.44 0-.83-.29-.96-.71s.04-.88.4-1.12c.01 0 2.55-1.9 2.55-8.17 0-3.86 3.14-7 7-7 .79 0 1.5.11 2.22.35.52.17.81.74.63 1.27-.17.52-.74.81-1.27.63-.52-.17-1-.25-1.58-.25-2.76 0-5 2.24-5 5 0 4-.95 6.52-1.86 8h13.74q-.18-.285-.36-.63a.993.993 0 0 1 .41-1.35.993.993 0 0 1 1.35.41c.67 1.25 1.32 1.77 1.33 1.78.33.26.47.71.33 1.12a1 1 0 0 1-.94.68z" /><path d="M67.003 22c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4m0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2" /></g><circle cx={67} cy={18} r={4} fill="#D83F20" /></g></svg>;
export default Component;