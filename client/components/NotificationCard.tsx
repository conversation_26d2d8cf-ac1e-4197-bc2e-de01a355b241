interface NotificationCardProps {
  type: 'warning' | 'error' | 'success';
  message: string;
  highlight: string;
}

export function NotificationCard({ type, message, highlight }: NotificationCardProps) {
  const bgColor = type === 'warning' ? 'bg-tertiary-50' : 
                  type === 'error' ? 'bg-focus-50' : 'bg-feature-50';
  
  const iconColor = type === 'warning' ? '#DA9A10' : 
                    type === 'error' ? '#D83F20' : '#219C63';

  return (
    <div className={`flex items-center gap-4 p-4 rounded-xl ${bgColor}`}>
      <div className="flex items-center gap-4 flex-1">
        {/* Warning Triangle Icon */}
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6 flex-shrink-0">
          <path d="M4 22.0075C3.49 22.0075 2.97002 21.8675 2.52002 21.6175C2.06002 21.3575 1.68 20.9775 1.41 20.5175C1.14 20.0575 1 19.5375 1 19.0175C1 18.4875 1.14002 17.9675 1.40002 17.5075L9.40002 3.5075C9.66002 3.0475 10.04 2.6675 10.5 2.3975C11.41 1.8675 12.6 1.8675 13.52 2.3975C13.98 2.6675 14.35 3.0475 14.62 3.5075L22.62 17.5075C22.88 17.9675 23.02 18.4775 23.02 19.0075C23.02 19.5375 22.88 20.0475 22.62 20.5075C22.36 20.9675 21.98 21.3375 21.52 21.6075C21.06 21.8675 20.55 22.0075 20.02 22.0075H3.99002H4ZM12.01 3.9975C11.83 3.9975 11.66 4.0475 11.51 4.1375C11.36 4.2275 11.23 4.3575 11.14 4.5075L3.14001 18.5075C3.05001 18.6675 3 18.8375 3 19.0175C3 19.1975 3.05001 19.3675 3.14001 19.5175C3.23001 19.6675 3.35001 19.7975 3.51001 19.8775C3.66001 19.9675 3.83001 19.9975 4.01001 20.0075H20.02C20.19 20.0075 20.37 19.9575 20.52 19.8775C20.67 19.7875 20.8 19.6675 20.89 19.5075C20.98 19.3575 21.02 19.1875 21.02 19.0075C21.02 18.8275 20.97 18.6575 20.89 18.5075L12.89 4.5075C12.8 4.3475 12.67 4.2175 12.52 4.1375C12.37 4.0575 12.19 3.9975 12.02 3.9975H12.01ZM12.03 18.0075C11.48 18.0075 11.03 17.5575 11.03 17.0075C11.03 16.4575 11.47 16.0075 12.03 16.0075C12.58 16.0075 13.03 16.4575 13.03 17.0075C13.03 17.5575 12.58 18.0075 12.03 18.0075ZM12.03 14.0075C11.48 14.0075 11.03 13.5575 11.03 13.0075V9.0075C11.03 8.4575 11.48 8.0075 12.03 8.0075C12.58 8.0075 13.03 8.4575 13.03 9.0075V13.0075C13.03 13.5575 12.58 14.0075 12.03 14.0075Z" fill={iconColor}/>
        </svg>

        {/* Message */}
        <div className="flex-1">
          <p className="text-sm text-primary-900">
            {message} <span className="font-bold">{highlight}</span>
          </p>
        </div>
      </div>

      {/* Arrow Button */}
      <div className="p-2 bg-tertiary-600 rounded-full">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
          <path d="M9.00252 18.9825C8.74252 18.9825 8.4925 18.8825 8.2925 18.6925C7.9025 18.3025 7.9025 17.6725 8.2925 17.2825L13.5825 11.9925L8.2925 6.7025C7.9025 6.3125 7.9025 5.6825 8.2925 5.2925C8.6825 4.9025 9.31253 4.9025 9.70253 5.2925L15.7025 11.2925C16.0925 11.6825 16.0925 12.3125 15.7025 12.7025L9.70253 18.7025C9.50253 18.9025 9.25251 18.9925 8.99251 18.9925L9.00252 18.9825Z" fill="white"/>
        </svg>
      </div>
    </div>
  );
}
