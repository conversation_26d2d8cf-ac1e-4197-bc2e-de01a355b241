import { useState } from 'react';
import AgendaPanel from './AgendaPanel';

interface AppointmentProps {
  time: string;
  status: 'waiting' | 'confirmed';
  clientName: string;
  location: string;
  shift: string;
}

function AppointmentCard({ time, status, clientName, location, shift }: AppointmentProps) {
  const statusConfig = {
    waiting: {
      icon: (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="w-4 h-4">
          <path d="M12.6667 15.3334H3.33334C2.96667 15.3334 2.66667 15.0334 2.66667 14.6667C2.66667 14.3 2.96667 14 3.33334 14H4.00001V11.8867C4.00001 11.36 4.21334 10.8467 4.58667 10.4734L7.05998 8.00002L4.58667 5.52667C4.21334 5.15334 4.00001 4.64001 4.00001 4.11334V2.00002H3.33334C2.96667 2.00002 2.66667 1.70002 2.66667 1.33335C2.66667 0.966687 2.96667 0.666687 3.33334 0.666687H12.6667C13.0333 0.666687 13.3333 0.966687 13.3333 1.33335C13.3333 1.70002 13.0333 2.00002 12.6667 2.00002H12V4.11334C12 4.64001 11.7867 5.15334 11.4133 5.52667L8.93999 8.00002L11.4133 10.4734C11.7867 10.8467 12 11.36 12 11.8867V14H12.6667C13.0333 14 13.3333 14.3 13.3333 14.6667C13.3333 15.0334 13.0333 15.3334 12.6667 15.3334ZM5.33334 14H10.6667V11.8867C10.6667 11.7134 10.5933 11.54 10.4733 11.4134L8 8.94L5.52666 11.4134C5.39999 11.54 5.33334 11.7067 5.33334 11.8867V14ZM5.33334 2.00002V4.11334C5.33334 4.28668 5.40666 4.46002 5.52666 4.58669L8 7.06004L10.4733 4.58669C10.6 4.46002 10.6667 4.29334 10.6667 4.11334V2.00002H5.33334Z" fill="#DA9A10"/>
        </svg>
      ),
      text: "Aguardando confirmação",
      color: "text-tertiary-600"
    },
    confirmed: {
      icon: (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="w-4 h-4">
          <path d="M8 15.3333C3.95334 15.3333 0.666672 12.0466 0.666672 7.99996C0.666672 3.95329 3.95334 0.666626 8 0.666626C12.0467 0.666626 15.3333 3.95329 15.3333 7.99996C15.3333 12.0466 12.0467 15.3333 8 15.3333ZM8 1.99996C4.69334 1.99996 2.00001 4.69329 2.00001 7.99996C2.00001 11.3066 4.69334 14 8 14C11.3067 14 14 11.3066 14 7.99996C14 4.69329 11.3067 1.99996 8 1.99996Z" fill="#219C63"/>
          <path d="M7.33338 9.99993C7.16004 9.99993 6.99338 9.93326 6.86004 9.80659L5.52671 8.47326C5.26671 8.21326 5.26671 7.79326 5.52671 7.53326C5.78671 7.27326 6.20671 7.27326 6.46671 7.53326L7.32671 8.39326L9.52004 6.19992C9.78004 5.93992 10.2 5.93992 10.46 6.19992C10.72 6.45992 10.72 6.87993 10.46 7.13993L7.79337 9.80659C7.66004 9.93993 7.49337 9.99993 7.32004 9.99993H7.33338Z" fill="#219C63"/>
        </svg>
      ),
      text: "Confirmado",
      color: "text-baseline-feature"
    }
  };

  const config = statusConfig[status];

  return (
    <div className="flex flex-col gap-2 p-4 border border-greyscale-200 rounded-xl">
      {/* Time and Status */}
      <div className="flex items-center justify-between">
        <div className="px-2 py-1 bg-baseline-primary rounded-lg">
          <span className="text-white font-bold text-sm">{time}</span>
        </div>
        <div className="flex items-center gap-2">
          {config.icon}
          <span className={`text-xs ${config.color}`}>
            {config.text}
          </span>
        </div>
      </div>

      {/* Client Info */}
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h4 className="font-bold text-black text-base">{clientName}</h4>
          <p className="font-bold text-secondary-400 text-sm">{location}</p>
          <p className="text-secondary-400 text-sm">{shift}</p>
        </div>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
          <path d="M9.00252 18.9825C8.74252 18.9825 8.4925 18.8825 8.2925 18.6925C7.9025 18.3025 7.9025 17.6725 8.2925 17.2825L13.5825 11.9925L8.2925 6.7025C7.9025 6.3125 7.9025 5.6825 8.2925 5.2925C8.6825 4.9025 9.31253 4.9025 9.70253 5.2925L15.7025 11.2925C16.0925 11.6825 16.0925 12.3125 15.7025 12.7025L9.70253 18.7025C9.50253 18.9025 9.25251 18.9925 8.99251 18.9925L9.00252 18.9825Z" fill="#080F12"/>
        </svg>
      </div>
    </div>
  );
}

export function UpcomingAppointments() {
  const [isAgendaOpen, setIsAgendaOpen] = useState(false);

  const toggleAgenda = () => setIsAgendaOpen((v) => !v);

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between px-4 pt-6 pb-4">
        <h2 className="text-lg font-bold text-primary-900">Prróximos atendimentos</h2>
        <button
          onClick={toggleAgenda}
          aria-expanded={isAgendaOpen}
          className="flex items-center gap-2 transition-all duration-200 hover:scale-105 active:scale-95 p-1 rounded-lg hover:bg-primary-50"
        >
          <span className="text-sm font-bold text-baseline-secondary">
            {isAgendaOpen ? 'Fechar' : 'Ver agenda'}
          </span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className={`w-5 h-5 transition-transform duration-200 ${isAgendaOpen ? 'rotate-180' : ''}`}>
            <path d="M7.50209 15.8188C7.28542 15.8188 7.07707 15.7354 6.91041 15.5771C6.58541 15.2521 6.58541 14.7271 6.91041 14.4021L11.3188 9.99378L6.91041 5.58544C6.58541 5.26044 6.58541 4.73544 6.91041 4.41044C7.23541 4.08544 7.76043 4.08544 8.08543 4.41044L13.0854 9.41044C13.4104 9.73544 13.4104 10.2604 13.0854 10.5854L8.08543 15.5854C7.91877 15.7521 7.71042 15.8271 7.49375 15.8271L7.50209 15.8188Z" fill="#3E7998"/>
          </svg>
        </button>
      </div>

      {/* Inline expandable agenda */}
      <div className={`overflow-hidden transition-all duration-300 ${isAgendaOpen ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'}`}>
        {isAgendaOpen && (
          <AgendaPanel onClose={() => setIsAgendaOpen(false)} />
        )}
      </div>

      {/* Appointments List */}
      <div className="px-4 space-y-2">
        <AppointmentCard
          time="14:45"
          status="waiting"
          clientName="Raphael Luviam"
          location="Mega Park Taubaté"
          shift="Plantão Nome do Plantão"
        />
        <AppointmentCard
          time="15:30"
          status="confirmed"
          clientName="Raphael Luviam"
          location="Mega Park Taubaté"
          shift="Plantão Nome do Plantão"
        />
      </div>
    </div>
  );
}
