import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface CompanyCodeFormProps {
  onSubmit: (code: string) => void;
  onGoBack: () => void;
  isLoading: boolean;
}

export function CompanyCodeForm({ onSubmit, onGoBack, isLoading }: CompanyCodeFormProps) {
  const [code, setCode] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (code.trim()) {
      onSubmit(code);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      <div className="flex items-center p-4 h-24 bg-white">
        <button
          onClick={onGoBack}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          disabled={isLoading}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <h1 className="text-[24px] font-medium text-[#191919] ml-4">
          Crie sua conta
        </h1>
      </div>

      {/* Form Content */}
      <div className="px-4 pt-8">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Company Code Input */}
          <div className="space-y-2">
            <label className="text-[16px] font-medium text-[#1D2025]">
              Código da empresa
            </label>
            <Input
              type="text"
              placeholder="23542345342"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="h-12 px-4 text-base border-[#E0E0E0] rounded-lg focus:ring-2 focus:ring-[#203C47] focus:border-transparent"
              disabled={isLoading}
            />
          </div>

          {/* Continue Button */}
          <Button
            type="submit"
            disabled={!code.trim() || isLoading}
            className="w-full h-12 bg-[#203C47] hover:bg-[#203C47]/90 text-white font-bold text-[16px] rounded-xl disabled:bg-[#E0E0E0] disabled:text-[#9F9F9F]"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="animate-spin">
                  <path d="M12 22.005C10.96 22.005 9.92 21.845 8.91 21.515C6.89 20.855 5.16 19.605 3.91 17.885C2.66 16.165 2 14.135 2 12.005C2 9.87501 2.66 7.845 3.91 6.125C5.16 4.405 6.89 3.145 8.91 2.495C10.93 1.835 13.07 1.835 15.09 2.495C15.62 2.665 15.9 3.22501 15.73 3.75501C15.56 4.28501 15 4.56502 14.47 4.39502C12.85 3.86502 11.14 3.86502 9.53 4.39502C7.91 4.91502 6.53 5.925 5.53 7.305C4.53 8.685 4 10.305 4 12.005C4 13.705 4.53 15.335 5.53 16.705C6.53 18.085 7.91 19.085 9.53 19.605C11.15 20.135 12.86 20.135 14.47 19.605C16.08 19.075 17.47 18.075 18.47 16.695C19.47 15.315 20 13.695 20 11.995C20 11.445 20.45 10.995 21 10.995C21.55 10.995 22 11.445 22 11.995C22 14.115 21.34 16.155 20.09 17.875C18.84 19.595 17.11 20.855 15.09 21.505C14.08 21.835 13.04 21.995 12 21.995V22.005Z" fill="currentColor"/>
                </svg>
                Continuar
              </div>
            ) : (
              'Continuar'
            )}
          </Button>

          {/* Information Card */}
          <div className="flex gap-4 p-4 bg-[#DFEDF5] rounded-xl mt-6">
            <div className="flex-shrink-0">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M19 23H5C3.35 23 2 21.65 2 20V13C2 11.35 3.35 10 5 10H19C20.65 10 22 11.35 22 13V20C22 21.65 20.65 23 19 23ZM5 12C4.45 12 4 12.45 4 13V20C4 20.55 4.45 21 5 21H19C19.55 21 20 20.55 20 20V13C20 12.45 19.55 12 19 12H5Z" fill="#3E7998"/>
                <path d="M17 12C16.45 12 16 11.55 16 11V7C16 5.95 15.57 4.91998 14.83 4.16998C14.08 3.41998 13.07 3 12 3C10.93 3 9.92998 3.41998 9.16998 4.16998C8.42998 4.90998 8 5.95 8 7V11C8 11.55 7.55 12 7 12C6.45 12 6 11.55 6 11V7C6 5.4 6.62001 3.89001 7.76001 2.76001C8.90001 1.63001 10.4 1 12 1C13.6 1 15.11 1.62001 16.24 2.76001C17.37 3.90001 18 5.4 18 7V11C18 11.55 17.55 12 17 12Z" fill="#3E7998"/>
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-[14px] font-medium text-[#080F12] leading-4">
                Se você não tem o código da empresa, entre em contato com o RH ou com o seu gestor.
              </p>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
