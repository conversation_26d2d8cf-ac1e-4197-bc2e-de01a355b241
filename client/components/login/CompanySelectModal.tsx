import { Company } from "@/pages/Login";

interface CompanySelectModalProps {
  companies: Company[];
  onSelect: (company: Company) => void;
  onClose: () => void;
}

export function CompanySelectModal({ companies, onSelect, onClose }: CompanySelectModalProps) {
  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center bg-black/50">
      {/* Modal Content */}
      <div className="w-full max-w-sm bg-white rounded-t-2xl shadow-xl animate-slide-up">
        {/* Header */}
        <div className="flex items-center justify-between p-4 pt-2">
          <div className="w-12 h-1 bg-black rounded-full mx-auto"></div>
          <button
            onClick={onClose}
            className="absolute right-4 top-6 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <path d="M23.99 25.3099C23.6433 25.3099 23.31 25.1765 23.0433 24.9232L15.99 17.87L8.93663 24.9232C8.41663 25.4432 7.57663 25.4432 7.05663 24.9232C6.53663 24.4032 6.53663 23.5633 7.05663 23.0433L14.11 15.9899L7.05663 8.93655C6.53663 8.41655 6.53663 7.57667 7.05663 7.05667C7.57663 6.53667 8.41663 6.53667 8.93663 7.05667L15.99 14.1099L23.0433 7.05667C23.5633 6.53667 24.4033 6.53667 24.9233 7.05667C25.4433 7.57667 25.4433 8.41655 24.9233 8.93655L17.87 15.9899L24.9233 23.0433C25.4433 23.5633 25.4433 24.4032 24.9233 24.9232C24.6566 25.1899 24.3233 25.3099 23.9766 25.3099H23.99Z" fill="#203C47"/>
            </svg>
          </button>
        </div>

        {/* Title */}
        <div className="px-4 pb-4">
          <h2 className="text-[22px] font-bold text-[#191919]">
            Escolha o empresa
          </h2>
        </div>

        {/* Company List */}
        <div className="px-4 pb-8 space-y-0 max-h-[400px] overflow-y-auto">
          {companies.map((company, index) => (
            <div key={company.id}>
              <button
                onClick={() => onSelect(company)}
                className="flex items-center gap-4 w-full p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-white border border-[#E0E0E0] rounded-lg">
                  {company.logo ? (
                    <img src={company.logo} alt={company.name} className="w-8 h-8" />
                  ) : (
                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
                      <path d="M8.75004 12.8333H5.36671C4.71331 12.8333 4.38662 12.8333 4.13705 12.9605C3.91753 13.0723 3.73905 13.2508 3.6272 13.4703C3.50004 13.7199 3.50004 14.0466 3.50004 14.7V24.5M19.25 12.8333H22.6334C23.2868 12.8333 23.6135 12.8333 23.863 12.9605C24.0826 13.0723 24.261 13.2508 24.3729 13.4703C24.5 13.7199 24.5 14.0466 24.5 14.7V24.5M19.25 24.5V7.23333C19.25 5.92654 19.25 5.27315 18.9957 4.77402C18.772 4.33498 18.4151 3.97802 17.976 3.75432C17.4769 3.5 16.8235 3.5 15.5167 3.5H12.4834C11.1766 3.5 10.5232 3.5 10.0241 3.75432C9.58502 3.97802 9.22806 4.33498 9.00436 4.77402C8.75004 5.27315 8.75004 5.92654 8.75004 7.23333V24.5M25.6667 24.5H2.33337M12.8334 8.16667H15.1667M12.8334 12.8333H15.1667M12.8334 17.5H15.1667" stroke="#3E7998" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  )}
                </div>
                <div className="flex flex-col items-start">
                  <span className="text-[18px] font-bold text-black leading-6">
                    {company.name}
                  </span>
                </div>
              </button>
              
              {/* Divider */}
              {index < companies.length - 1 && (
                <div className="h-px bg-[#E0E0E0] mx-4"></div>
              )}
            </div>
          ))}
        </div>

        {/* Home Indicator */}
        <div className="flex justify-center py-2 pb-8">
          <div className="w-[139px] h-[5px] bg-black rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
