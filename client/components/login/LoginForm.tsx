import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface LoginFormProps {
  onContinue: (email: string) => void;
  onCreateAccount: () => void;
  isLoading: boolean;
}

export function LoginForm({ onContinue, onCreateAccount, isLoading }: LoginFormProps) {
  const [email, setEmail] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email.trim()) {
      onContinue(email);
    }
  };

  return (
    <div className="flex flex-col items-center gap-6 px-4 pt-16 pb-8">
      {/* Logo */}
      <div className="flex items-center justify-center gap-4 mb-8">
        <svg width="169" height="40" viewBox="0 0 169 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M34.3305 4.06781H21.4491L28.2288 29.4915L34.3305 4.06781Z" fill="#203C47"/>
          <path d="M36.3644 36.6102H32.6356L36.3644 21.3559V36.6102Z" fill="#203C47"/>
          <path d="M12.6356 28.8136L6.1949 4.06781H16.0254L17.7203 10.5085L12.6356 28.8136Z" fill="#203C47"/>
          <path d="M8.2288 36.6102H3.82202V20.678L8.2288 36.6102Z" fill="#203C47"/>
          <path d="M24.5 36.6102H15.6864L20.0932 20.339L24.5 36.6102Z" fill="#203C47"/>
          <path d="M156.353 28.9929L160.88 22.4364L156.353 15.88H159.163L162.598 20.9794L166.058 15.88H168.842L164.315 22.4364L168.842 28.9929H166.058L162.598 23.8674L159.163 28.9929H156.353Z" fill="#203C47"/>
          <path d="M148.621 29.3051C147.389 29.3051 146.279 29.0189 145.291 28.4465C144.319 27.8741 143.547 27.0762 142.975 26.0529C142.42 25.0122 142.142 23.8154 142.142 22.4624C142.142 21.0748 142.429 19.8693 143.001 18.846C143.573 17.8053 144.354 16.9987 145.343 16.4263C146.331 15.8539 147.441 15.5677 148.673 15.5677C149.922 15.5677 151.032 15.8539 152.003 16.4263C152.974 16.9987 153.738 17.7966 154.293 18.82C154.865 19.8433 155.151 21.0488 155.151 22.4364C155.151 23.824 154.865 25.0295 154.293 26.0529C153.738 27.0762 152.966 27.8741 151.977 28.4465C150.988 29.0189 149.87 29.3051 148.621 29.3051ZM148.621 27.0676C149.332 27.0676 149.974 26.8941 150.546 26.5472C151.136 26.2003 151.604 25.6886 151.951 25.0122C152.315 24.3184 152.498 23.4598 152.498 22.4364C152.498 21.4131 152.324 20.5631 151.977 19.8867C151.63 19.1929 151.162 18.6725 150.572 18.3256C150 17.9787 149.367 17.8053 148.673 17.8053C147.979 17.8053 147.337 17.9787 146.748 18.3256C146.158 18.6725 145.681 19.1929 145.317 19.8867C144.97 20.5631 144.796 21.4131 144.796 22.4364C144.796 23.4598 144.97 24.3184 145.317 25.0122C145.681 25.6886 146.149 26.2003 146.722 26.5472C147.311 26.8941 147.944 27.0676 148.621 27.0676Z" fill="#203C47"/>
          <path d="M122.223 28.9929L117.28 10.7805H120.063L123.758 25.9488L127.999 10.7805H130.887L134.998 26.0008L138.744 10.7805H141.528L136.507 28.9929H133.463L129.378 14.501L125.189 28.9929H122.223Z" fill="#203C47"/>
          <path d="M112.05 28.9929V15.88H114.652V28.9929H112.05ZM113.377 13.4083C112.874 13.4083 112.458 13.2522 112.128 12.94C111.816 12.6278 111.66 12.2288 111.66 11.7432C111.66 11.2748 111.816 10.8933 112.128 10.5984C112.458 10.2862 112.874 10.1301 113.377 10.1301C113.863 10.1301 114.27 10.2862 114.6 10.5984C114.929 10.8933 115.094 11.2748 115.094 11.7432C115.094 12.2288 114.929 12.6278 114.6 12.94C114.27 13.2522 113.863 13.4083 113.377 13.4083Z" fill="#203C47"/>
          <path d="M102.508 29.3051C101.796 29.3051 101.146 29.201 100.556 28.9929C99.9839 28.8021 99.4809 28.5332 99.0473 28.1863C98.6137 27.8394 98.2494 27.4405 97.9546 26.9895L97.6944 28.9929H95.3528V10.2601H97.9546V17.9353C98.3708 17.2762 98.9519 16.7212 99.6977 16.2702C100.461 15.8019 101.398 15.5677 102.508 15.5677C103.756 15.5677 104.858 15.8713 105.812 16.4784C106.766 17.0681 107.503 17.8833 108.023 18.924C108.561 19.9474 108.83 21.1269 108.83 22.4624C108.83 23.7633 108.561 24.9341 108.023 25.9748C107.503 27.0155 106.766 27.8307 105.812 28.4205C104.858 29.0102 103.756 29.3051 102.508 29.3051ZM102.091 27.0415C102.889 27.0415 103.592 26.8507 104.199 26.4692C104.823 26.0876 105.309 25.5499 105.656 24.8561C106.02 24.1622 106.202 23.3557 106.202 22.4364C106.202 21.5171 106.02 20.7192 105.656 20.0428C105.309 19.349 104.823 18.8113 104.199 18.4297C103.592 18.0307 102.889 17.8313 102.091 17.8313C101.276 17.8313 100.556 18.0307 99.9319 18.4297C99.3248 18.8113 98.8478 19.349 98.5009 20.0428C98.154 20.7192 97.9806 21.5171 97.9806 22.4364C97.9806 23.3557 98.154 24.1622 98.5009 24.8561C98.8478 25.5499 99.3248 26.0876 99.9319 26.4692C100.556 26.8507 101.276 27.0415 102.091 27.0415Z" fill="#203C47"/>
          <path d="M85.7617 29.3051C84.5302 29.3051 83.4201 29.0189 82.4315 28.4465C81.4601 27.8741 80.6883 27.0762 80.1159 26.0529C79.5608 25.0122 79.2833 23.8154 79.2833 22.4624C79.2833 21.0748 79.5695 19.8693 80.1419 18.846C80.7143 17.8053 81.4948 16.9987 82.4835 16.4263C83.4722 15.8539 84.5823 15.5677 85.8138 15.5677C87.0626 15.5677 88.1727 15.8539 89.144 16.4263C90.1154 16.9987 90.8785 17.7966 91.4336 18.82C92.006 19.8433 92.2922 21.0488 92.2922 22.4364C92.2922 23.824 92.006 25.0295 91.4336 26.0529C90.8785 27.0762 90.1067 27.8741 89.118 28.4465C88.1293 29.0189 87.0106 29.3051 85.7617 29.3051ZM85.7617 27.0676C86.4729 27.0676 87.1146 26.8941 87.687 26.5472C88.2768 26.2003 88.7451 25.6886 89.092 25.0122C89.4562 24.3184 89.6384 23.4598 89.6384 22.4364C89.6384 21.4131 89.4649 20.5631 89.118 19.8867C88.7711 19.1929 88.3028 18.6725 87.7131 18.3256C87.1407 17.9787 86.5076 17.8053 85.8138 17.8053C85.12 17.8053 84.4782 17.9787 83.8885 18.3256C83.2987 18.6725 82.8217 19.1929 82.4575 19.8867C82.1106 20.5631 81.9371 21.4131 81.9371 22.4364C81.9371 23.4598 82.1106 24.3184 82.4575 25.0122C82.8217 25.6886 83.29 26.2003 83.8624 26.5472C84.4522 26.8941 85.0853 27.0676 85.7617 27.0676Z" fill="#203C47"/>
          <path d="M56.1738 28.9929V15.88H58.5154L58.6975 17.7272C59.1138 17.0508 59.6689 16.5217 60.3627 16.1401C61.0565 15.7585 61.837 15.5677 62.7043 15.5677C63.3634 15.5677 63.9618 15.6631 64.4995 15.8539C65.0372 16.0274 65.5142 16.2962 65.9305 16.6605C66.3467 17.0247 66.685 17.4757 66.9452 18.0134C67.4135 17.2502 68.0379 16.6518 68.8184 16.2182C69.6163 15.7846 70.4575 15.5677 71.3421 15.5677C72.4002 15.5677 73.3108 15.7846 74.074 16.2182C74.8372 16.6345 75.4182 17.2676 75.8172 18.1175C76.2161 18.95 76.4156 19.9908 76.4156 21.2396V28.9929H73.8398V21.4998C73.8398 20.2856 73.5883 19.3663 73.0853 18.7419C72.5997 18.1175 71.8798 17.8053 70.9259 17.8053C70.2841 17.8053 69.7117 17.97 69.2087 18.2996C68.7057 18.6292 68.3067 19.1062 68.0119 19.7306C67.7344 20.355 67.5956 21.1182 67.5956 22.0201V28.9929H64.9938V21.4998C64.9938 20.2856 64.7423 19.3663 64.2393 18.7419C63.7536 18.1175 63.0338 17.8053 62.0798 17.8053C61.4728 17.8053 60.9177 17.97 60.4147 18.2996C59.9117 18.6292 59.5128 19.1062 59.2179 19.7306C58.923 20.355 58.7756 21.1182 58.7756 22.0201V28.9929H56.1738Z" fill="#203C47"/>
          <path d="M49.9011 28.9929V10.7805H52.5029V28.9929H49.9011Z" fill="#203C47"/>
        </svg>
      </div>

      {/* Title */}
      <h1 className="text-[16px] font-bold text-center text-[#191919] mb-6">
        Entre com sua conta
      </h1>

      {/* Form */}
      <form onSubmit={handleSubmit} className="w-full max-w-[343px] space-y-4">
        {/* User Input */}
        <div className="space-y-2">
          <label className="text-[16px] font-medium text-[#1D2025]">
            Usuário
          </label>
          <Input
            type="text"
            placeholder="E-mail, CPF ou CNPJ"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="h-12 px-4 text-base border-[#E0E0E0] rounded-lg focus:ring-2 focus:ring-[#203C47] focus:border-transparent"
            disabled={isLoading}
          />
        </div>

        {/* Continue Button */}
        <Button
          type="submit"
          disabled={!email.trim() || isLoading}
          className="w-full h-12 bg-[#203C47] hover:bg-[#203C47]/90 text-white font-bold text-[16px] rounded-xl disabled:bg-[#E0E0E0] disabled:text-[#9F9F9F]"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="animate-spin">
                <path d="M12 22.005C10.96 22.005 9.92 21.845 8.91 21.515C6.89 20.855 5.16 19.605 3.91 17.885C2.66 16.165 2 14.135 2 12.005C2 9.87501 2.66 7.845 3.91 6.125C5.16 4.405 6.89 3.145 8.91 2.495C10.93 1.835 13.07 1.835 15.09 2.495C15.62 2.665 15.9 3.22501 15.73 3.75501C15.56 4.28501 15 4.56502 14.47 4.39502C12.85 3.86502 11.14 3.86502 9.53 4.39502C7.91 4.91502 6.53 5.925 5.53 7.305C4.53 8.685 4 10.305 4 12.005C4 13.705 4.53 15.335 5.53 16.705C6.53 18.085 7.91 19.085 9.53 19.605C11.15 20.135 12.86 20.135 14.47 19.605C16.08 19.075 17.47 18.075 18.47 16.695C19.47 15.315 20 13.695 20 11.995C20 11.445 20.45 10.995 21 10.995C21.55 10.995 22 11.445 22 11.995C22 14.115 21.34 16.155 20.09 17.875C18.84 19.595 17.11 20.855 15.09 21.505C14.08 21.835 13.04 21.995 12 21.995V22.005Z" fill="currentColor"/>
              </svg>
              Continuar
            </div>
          ) : (
            'Continuar'
          )}
        </Button>
      </form>

      {/* Divider */}
      <div className="flex items-center gap-4 w-full max-w-[343px] my-6">
        <div className="flex-1 h-px bg-[#E0E0E0]" />
        <span className="text-[16px] font-medium text-[#8A8A8A]">ou</span>
        <div className="flex-1 h-px bg-[#E0E0E0]" />
      </div>

      {/* Create Account Button */}
      <Button
        type="button"
        variant="outline"
        onClick={onCreateAccount}
        className="w-full max-w-[343px] h-12 border-[#203C47] text-[#203C47] font-bold text-[16px] rounded-xl hover:bg-[#203C47]/5"
        disabled={isLoading}
      >
        Crie sua conta
      </Button>
    </div>
  );
}
