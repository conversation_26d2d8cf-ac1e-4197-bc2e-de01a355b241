import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Company } from "@/pages/Login";

interface CreateAccountFormProps {
  selectedCompany: Company | null;
  onSubmit: (userData: any) => void;
  onGoBack: () => void;
  onSelectCompany: () => void;
  isLoading: boolean;
}

export function CreateAccountForm({ 
  selectedCompany, 
  onSubmit, 
  onGoBack, 
  onSelectCompany, 
  isLoading 
}: CreateAccountFormProps) {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    confirmPassword: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.username && formData.password && formData.password === formData.confirmPassword) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isFormValid = formData.username && formData.password && formData.password === formData.confirmPassword;

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      <div className="flex items-center p-4 h-24 bg-white">
        <button
          onClick={onGoBack}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          disabled={isLoading}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <h1 className="text-[24px] font-medium text-[#191919] ml-4">
          Crie sua conta
        </h1>
      </div>

      {/* Form Content */}
      <div className="px-4 pt-8">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Company Selection */}
          {selectedCompany && (
            <button
              type="button"
              onClick={onSelectCompany}
              className="flex items-center gap-4 p-3 w-full bg-[#ECEFF1] rounded-xl hover:bg-[#ECEFF1]/80 transition-colors"
              disabled={isLoading}
            >
              <div className="flex items-center justify-center w-12 h-12 bg-white border border-[#E0E0E0] rounded-lg">
                {selectedCompany.logo ? (
                  <img src={selectedCompany.logo} alt={selectedCompany.name} className="w-8 h-8" />
                ) : (
                  <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
                    <path d="M8.75004 12.8333H5.36671C4.71331 12.8333 4.38662 12.8333 4.13705 12.9605C3.91753 13.0723 3.73905 13.2508 3.6272 13.4703C3.50004 13.7199 3.50004 14.0466 3.50004 14.7V24.5M19.25 12.8333H22.6334C23.2868 12.8333 23.6135 12.8333 23.863 12.9605C24.0826 13.0723 24.261 13.2508 24.3729 13.4703C24.5 13.7199 24.5 14.0466 24.5 14.7V24.5M19.25 24.5V7.23333C19.25 5.92654 19.25 5.27315 18.9957 4.77402C18.772 4.33498 18.4151 3.97802 17.976 3.75432C17.4769 3.5 16.8235 3.5 15.5167 3.5H12.4834C11.1766 3.5 10.5232 3.5 10.0241 3.75432C9.58502 3.97802 9.22806 4.33498 9.00436 4.77402C8.75004 5.27315 8.75004 5.92654 8.75004 7.23333V24.5M25.6667 24.5H2.33337M12.8334 8.16667H15.1667M12.8334 12.8333H15.1667M12.8334 17.5H15.1667" stroke="#3E7998" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
              </div>
              <div className="flex flex-col items-start">
                <span className="text-[14px] font-medium text-black">Empresa</span>
                <span className="text-[18px] font-bold text-black">{selectedCompany.name}</span>
              </div>
            </button>
          )}

          {/* Username Input */}
          <div className="space-y-2">
            <label className="text-[16px] font-medium text-[#1D2025]">
              Usuário
            </label>
            <Input
              type="text"
              placeholder="E-mail, CPF ou CNPJ"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              className="h-12 px-4 text-base border-[#E0E0E0] rounded-lg focus:ring-2 focus:ring-[#203C47] focus:border-transparent"
              disabled={isLoading}
            />
          </div>

          {/* Password Input */}
          <div className="space-y-2">
            <label className="text-[16px] font-medium text-[#1D2025]">
              Senha
            </label>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="***********"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="h-12 px-4 pr-12 text-base border-[#E0E0E0] rounded-lg focus:ring-2 focus:ring-[#203C47] focus:border-transparent"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1"
                disabled={isLoading}
              >
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                  <path fillRule="evenodd" clipRule="evenodd" d="M15.9751 17.0269C16.1251 17.1694 16.3126 17.2444 16.5076 17.2444V17.2519C16.7026 17.2519 16.8901 17.1844 17.0401 17.0344C17.3326 16.7419 17.3326 16.2694 17.0401 15.9769L11.1348 10.0716C11.1264 10.0627 11.1177 10.0541 11.1088 10.0456L7.95678 6.89356C7.94414 6.87993 7.93108 6.86689 7.91764 6.85442L2.03264 0.969375C1.74014 0.676875 1.26761 0.676875 0.97511 0.969375C0.68261 1.26188 0.68261 1.73438 0.97511 2.02688L3.81764 4.86938C2.55014 5.88938 1.5226 7.19439 0.825101 8.67189C0.727601 8.86689 0.727632 9.0919 0.810132 9.2869L0.81366 9.29473C0.960198 9.62077 3.37542 14.9944 9.00012 14.9944H9.02264C10.3951 14.9944 11.7376 14.6419 12.9301 13.9819L15.9751 17.0269ZM6.40249 7.45424L4.89013 5.94188C3.83374 6.76602 2.96443 7.81469 2.34206 9.00565C2.89253 10.0675 4.9699 13.4944 9.00763 13.4944H9.02264C9.99764 13.4944 10.9501 13.2769 11.8201 12.8719L10.5467 11.5984C10.4274 11.674 10.302 11.7414 10.1701 11.7994C9.7951 11.9644 9.4051 12.0544 9.0001 12.0619H8.94759V12.0469C8.56509 12.0469 8.18259 11.9719 7.82259 11.8294C7.44759 11.6794 7.11008 11.4544 6.82508 11.1694C6.54008 10.8844 6.31508 10.5469 6.16508 10.1719C6.01508 9.78942 5.94009 9.39942 5.94759 8.99442C5.94759 8.58942 6.03761 8.19942 6.20261 7.83193C6.26084 7.6996 6.32746 7.57382 6.40249 7.45424ZM9.42755 10.4793L7.52269 8.57445C7.47373 8.72008 7.44759 8.87007 7.44759 9.02442C7.44759 9.22692 7.47761 9.42191 7.56011 9.61691C7.63511 9.80441 7.74762 9.96941 7.89011 10.1119C8.03261 10.2544 8.20509 10.3669 8.38509 10.4419C8.57259 10.5169 8.77508 10.5544 8.97758 10.5544C9.13192 10.5544 9.28192 10.5283 9.42755 10.4793Z" fill="#1D2025"/>
                  <path d="M14.7826 11.5894C14.9251 11.7019 15.0901 11.7544 15.2551 11.7544C15.4726 11.7544 15.6901 11.6644 15.8401 11.4769C16.3726 10.8169 16.8301 10.0894 17.1901 9.31691C17.2801 9.12191 17.2801 8.89691 17.1976 8.7019C17.1001 8.4694 14.6851 2.99442 9.00761 2.99442C8.66261 2.99442 8.3101 3.01691 7.9651 3.06191C7.5526 3.10691 7.2601 3.48192 7.3126 3.89442C7.3576 4.30692 7.73259 4.59192 8.14509 4.54692C8.43009 4.50942 8.72261 4.49442 9.00761 4.49442C13.0501 4.49442 15.1351 7.92941 15.6751 8.98691C15.3901 9.53441 15.0526 10.0519 14.6701 10.5319C14.4076 10.8544 14.4601 11.3269 14.7826 11.5894Z" fill="#1D2025"/>
                </svg>
              </button>
            </div>
          </div>

          {/* Confirm Password Input */}
          <div className="space-y-2">
            <label className="text-[16px] font-medium text-[#1D2025]">
              Repetir senha
            </label>
            <div className="relative">
              <Input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="***********"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className="h-12 px-4 pr-12 text-base border-[#E0E0E0] rounded-lg focus:ring-2 focus:ring-[#203C47] focus:border-transparent"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1"
                disabled={isLoading}
              >
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                  <path fillRule="evenodd" clipRule="evenodd" d="M15.9751 17.0269C16.1251 17.1694 16.3126 17.2444 16.5076 17.2444V17.2519C16.7026 17.2519 16.8901 17.1844 17.0401 17.0344C17.3326 16.7419 17.3326 16.2694 17.0401 15.9769L11.1348 10.0716C11.1264 10.0627 11.1177 10.0541 11.1088 10.0456L7.95678 6.89356C7.94414 6.87993 7.93108 6.86689 7.91764 6.85442L2.03264 0.969375C1.74014 0.676875 1.26761 0.676875 0.97511 0.969375C0.68261 1.26188 0.68261 1.73438 0.97511 2.02688L3.81764 4.86938C2.55014 5.88938 1.5226 7.19439 0.825101 8.67189C0.727601 8.86689 0.727632 9.0919 0.810132 9.2869L0.81366 9.29473C0.960198 9.62077 3.37542 14.9944 9.00012 14.9944H9.02264C10.3951 14.9944 11.7376 14.6419 12.9301 13.9819L15.9751 17.0269ZM6.40249 7.45424L4.89013 5.94188C3.83374 6.76602 2.96443 7.81469 2.34206 9.00565C2.89253 10.0675 4.9699 13.4944 9.00763 13.4944H9.02264C9.99764 13.4944 10.9501 13.2769 11.8201 12.8719L10.5467 11.5984C10.4274 11.674 10.302 11.7414 10.1701 11.7994C9.7951 11.9644 9.4051 12.0544 9.0001 12.0619H8.94759V12.0469C8.56509 12.0469 8.18259 11.9719 7.82259 11.8294C7.44759 11.6794 7.11008 11.4544 6.82508 11.1694C6.54008 10.8844 6.31508 10.5469 6.16508 10.1719C6.01508 9.78942 5.94009 9.39942 5.94759 8.99442C5.94759 8.58942 6.03761 8.19942 6.20261 7.83193C6.26084 7.6996 6.32746 7.57382 6.40249 7.45424ZM9.42755 10.4793L7.52269 8.57445C7.47373 8.72008 7.44759 8.87007 7.44759 9.02442C7.44759 9.22692 7.47761 9.42191 7.56011 9.61691C7.63511 9.80441 7.74762 9.96941 7.89011 10.1119C8.03261 10.2544 8.20509 10.3669 8.38509 10.4419C8.57259 10.5169 8.77508 10.5544 8.97758 10.5544C9.13192 10.5544 9.28192 10.5283 9.42755 10.4793Z" fill="#1D2025"/>
                  <path d="M14.7826 11.5894C14.9251 11.7019 15.0901 11.7544 15.2551 11.7544C15.4726 11.7544 15.6901 11.6644 15.8401 11.4769C16.3726 10.8169 16.8301 10.0894 17.1901 9.31691C17.2801 9.12191 17.2801 8.89691 17.1976 8.7019C17.1001 8.4694 14.6851 2.99442 9.00761 2.99442C8.66261 2.99442 8.3101 3.01691 7.9651 3.06191C7.5526 3.10691 7.2601 3.48192 7.3126 3.89442C7.3576 4.30692 7.73259 4.59192 8.14509 4.54692C8.43009 4.50942 8.72261 4.49442 9.00761 4.49442C13.0501 4.49442 15.1351 7.92941 15.6751 8.98691C15.3901 9.53441 15.0526 10.0519 14.6701 10.5319C14.4076 10.8544 14.4601 11.3269 14.7826 11.5894Z" fill="#1D2025"/>
                </svg>
              </button>
            </div>
          </div>

          {/* Create Account Button */}
          <Button
            type="submit"
            disabled={!isFormValid || isLoading}
            className="w-full h-12 bg-[#203C47] hover:bg-[#203C47]/90 text-white font-bold text-[16px] rounded-xl disabled:bg-[#E0E0E0] disabled:text-[#9F9F9F] mt-6"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="animate-spin">
                  <path d="M12 22.005C10.96 22.005 9.92 21.845 8.91 21.515C6.89 20.855 5.16 19.605 3.91 17.885C2.66 16.165 2 14.135 2 12.005C2 9.87501 2.66 7.845 3.91 6.125C5.16 4.405 6.89 3.145 8.91 2.495C10.93 1.835 13.07 1.835 15.09 2.495C15.62 2.665 15.9 3.22501 15.73 3.75501C15.56 4.28501 15 4.56502 14.47 4.39502C12.85 3.86502 11.14 3.86502 9.53 4.39502C7.91 4.91502 6.53 5.925 5.53 7.305C4.53 8.685 4 10.305 4 12.005C4 13.705 4.53 15.335 5.53 16.705C6.53 18.085 7.91 19.085 9.53 19.605C11.15 20.135 12.86 20.135 14.47 19.605C16.08 19.075 17.47 18.075 18.47 16.695C19.47 15.315 20 13.695 20 11.995C20 11.445 20.45 10.995 21 10.995C21.55 10.995 22 11.445 22 11.995C22 14.115 21.34 16.155 20.09 17.875C18.84 19.595 17.11 20.855 15.09 21.505C14.08 21.835 13.04 21.995 12 21.995V22.005Z" fill="currentColor"/>
                </svg>
                Criar minha conta
              </div>
            ) : (
              'Criar minha conta'
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
