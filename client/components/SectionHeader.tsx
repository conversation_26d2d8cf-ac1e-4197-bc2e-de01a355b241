import ChevronRightIcon from "./icons/ChevronRightIcon";

interface SectionHeaderProps {
  title: string;
  actionText?: string;
  onActionClick?: () => void;
}

export function SectionHeader({ title, actionText, onActionClick }: SectionHeaderProps) {
  return (
    <div className="flex items-center justify-between px-4 mb-4">
      <h2 className="text-lg font-bold text-primary-900 tracking-tight">
        {title}
      </h2>
      
      {actionText && (
        <button 
          onClick={onActionClick}
          className="flex items-center gap-2 text-sm font-bold text-secondary-400 hover:text-secondary-400/80"
        >
          {actionText}
          <ChevronRightIcon width={7} height={12} color="#3E7998" />
        </button>
      )}
    </div>
  );
}