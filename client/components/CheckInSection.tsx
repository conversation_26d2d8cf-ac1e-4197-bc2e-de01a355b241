export function CheckInSection() {
  return (
    <div className="flex items-center justify-between px-4 bg-white">
      {/* Check-in Button */}
      <div className="flex items-center gap-2 px-4 py-3 bg-baseline-primary rounded-full">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
          <path d="M5.5 3L2.5 6M22.5 6L19.5 3M6.5 19L4.5 21M18.5 19L20.5 21M9.5 13.5L11.5 15.5L16 11M12.5 21C14.6217 21 16.6566 20.1571 18.1569 18.6569C19.6571 17.1566 20.5 15.1217 20.5 13C20.5 10.8783 19.6571 8.84344 18.1569 7.34315C16.6566 5.84285 14.6217 5 12.5 5C10.3783 5 8.34344 5.84285 6.84315 7.34315C5.34285 8.84344 4.5 10.8783 4.5 13C4.5 15.1217 5.34285 17.1566 6.84315 18.6569C8.34344 20.1571 10.3783 21 12.5 21Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        <span className="text-white font-bold text-sm">Check-in</span>
      </div>

      {/* User Actions */}
      <div className="flex items-center gap-1">
        {/* User Profile with notification */}
        <div className="relative p-2.5">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
            <path d="M12 23C5.93 23 1 18.07 1 12C1 5.93 5.93 1 12 1C18.07 1 23 5.93 23 12C23 18.07 18.07 23 12 23ZM12 3C7.04 3 3 7.04 3 12C3 16.96 7.04 21 12 21C16.96 21 21 16.96 21 12C21 7.04 16.96 3 12 3Z" fill="#203C47"/>
            <path d="M12 14C9.79 14 8 12.21 8 10C8 7.79 9.79 6 12 6C14.21 6 16 7.79 16 10C16 12.21 14.21 14 12 14ZM12 8C10.9 8 10 8.9 10 10C10 11.1 10.9 12 12 12C13.1 12 14 11.1 14 10C14 8.9 13.1 8 12 8Z" fill="#203C47"/>
            <path d="M17 21.66C16.45 21.66 16 21.21 16 20.66V19C16 18.74 15.9 18.48 15.71 18.3C15.51 18.1 15.27 18 15 18H9C8.73 18 8.48 18.1 8.3 18.29C8.11 18.48 8 18.74 8 19V20.66C8 21.21 7.55 21.66 7 21.66C6.45 21.66 6 21.21 6 20.66V19C6 18.21 6.32 17.43 6.88 16.88C7.44 16.32 8.19 16 9 16H15C15.81 16 16.56 16.32 17.13 16.89C17.68 17.43 18 18.21 18 19V20.66C18 21.21 17.55 21.66 17 21.66Z" fill="#203C47"/>
          </svg>
          {/* Red notification dot */}
          <div className="absolute top-2 right-1.5 w-2 h-2 bg-baseline-error rounded-full"></div>
        </div>

        {/* Notifications with notification */}
        <div className="relative p-2.5">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
            <path d="M12.0035 23C11.4835 23 10.9635 22.86 10.5035 22.59C10.0535 22.32 9.67344 21.94 9.42344 21.48C9.15344 21 9.33346 20.39 9.81346 20.12C10.3035 19.85 10.9034 20.03 11.1734 20.51C11.2534 20.66 11.3734 20.78 11.5234 20.87C11.8134 21.04 12.1934 21.04 12.4834 20.87C12.6234 20.79 12.7434 20.66 12.8234 20.51C13.0934 20.03 13.6935 19.85 14.1835 20.12C14.6635 20.39 14.8434 20.99 14.5734 21.48C14.3234 21.94 13.9434 22.33 13.4935 22.59C13.0335 22.86 12.5234 23 11.9935 23H12.0035ZM21.0035 18H3.00346C2.56346 18 2.17344 17.71 2.04344 17.29C1.91344 16.87 2.08346 16.41 2.44346 16.17C2.45346 16.17 4.99345 14.27 4.99345 8C4.99345 4.14 8.13345 1 11.9935 1C12.7835 1 13.4935 1.11001 14.2135 1.35001C14.7335 1.52001 15.0235 2.09 14.8435 2.62C14.6735 3.14 14.1034 3.43 13.5734 3.25C13.0534 3.08 12.5735 3 11.9935 3C9.23345 3 6.99345 5.24 6.99345 8C6.99345 12 6.04347 14.52 5.13347 16H18.8735C18.7535 15.81 18.6334 15.6 18.5134 15.37C18.2534 14.88 18.4334 14.28 18.9234 14.02C19.4134 13.76 20.0134 13.94 20.2734 14.43C20.9434 15.68 21.5934 16.2 21.6034 16.21C21.9334 16.47 22.0735 16.92 21.9335 17.33C21.7935 17.73 21.4235 18.01 20.9935 18.01L21.0035 18Z" fill="#203C47"/>
            <path d="M18.0034 12C15.7934 12 14.0034 10.21 14.0034 8C14.0034 5.79 15.7934 4 18.0034 4C20.2134 4 22.0034 5.79 22.0034 8C22.0034 10.21 20.2134 12 18.0034 12ZM18.0034 6C16.9034 6 16.0034 6.9 16.0034 8C16.0034 9.1 16.9034 10 18.0034 10C19.1034 10 20.0034 9.1 20.0034 8C20.0034 6.9 19.1034 6 18.0034 6Z" fill="#203C47"/>
          </svg>
          {/* Red notification dot */}
          <div className="absolute top-2 right-0.5 w-2 h-2 bg-baseline-error rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
