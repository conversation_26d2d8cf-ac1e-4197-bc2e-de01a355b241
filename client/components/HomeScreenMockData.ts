// Mock data for the home screen component

export const mockRootProps = {
  userName: "Júl<PERSON>" as const,
  currentTime: "6:43" as const,
  hasNotifications: true as const,
  studyProperty: {
    name: "Cidade Lapa - Água Branca" as const,
    image: "/images/property-cidade-lapa.png" as const
  } as const,
  hasUpcomingAppointments: false as const,
  hasNearbyDutyShifts: false as const,
  locationPermissionGranted: false as const
};

// Props types for the home screen component
export interface HomeScreenProps {
  userName: string;
  currentTime: string;
  hasNotifications: boolean;
  studyProperty: {
    name: string;
    image: string;
  };
  hasUpcomingAppointments: boolean;
  hasNearbyDutyShifts: boolean;
  locationPermissionGranted: boolean;
}