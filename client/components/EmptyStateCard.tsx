import { Card, CardContent } from "./ui/card";
import { But<PERSON> } from "./ui/button";

interface EmptyStateCardProps {
  icon: React.ReactNode;
  title: string;
  message: string;
  buttonText: string;
  onButtonClick?: () => void;
}

export function EmptyStateCard({ 
  icon, 
  title, 
  message, 
  buttonText, 
  onButtonClick 
}: EmptyStateCardProps) {
  return (
    <Card className="border-none shadow-none mx-4">
      <CardContent className="flex flex-col items-center space-y-4 p-4">
        {/* Icon */}
        <div className="flex justify-center">
          {icon}
        </div>
        
        {/* Title */}
        <h3 className="text-lg font-bold text-text-secondary text-center">
          {title}
        </h3>
        
        {/* Message */}
        <p className="text-base font-medium text-text-secondary text-center leading-5">
          {message}
        </p>
        
        {/* Action Button */}
        <Button 
          onClick={onButtonClick}
          className="bg-primary-600 hover:bg-primary-600/90 text-white font-bold text-base px-6 py-3 rounded-full"
          size="lg"
        >
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );
}