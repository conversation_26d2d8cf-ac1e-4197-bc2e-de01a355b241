interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
}

function NavItem({ icon, label, isActive = false }: NavItemProps) {
  const color = isActive ? "#305969" : "#8A8A8A";
  
  return (
    <div className="flex flex-col items-center gap-1 flex-1">
      <div style={{ color }}>
        {icon}
      </div>
      <span 
        className="text-sm"
        style={{ color }}
      >
        {label}
      </span>
    </div>
  );
}

export function BottomNavigation() {
  return (
    <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white border-t border-greyscale-200">
      {/* Navigation Items */}
      <div className="flex items-center justify-center gap-4 px-4 py-2">
        <NavItem
          isActive={true}
          icon={
            <svg width="20" height="20" viewBox="0 0 21 20" fill="none" className="w-5 h-5">
              <path d="M5.06667 19.1667C4.40834 19.1667 3.76667 18.9 3.30001 18.4333C2.83334 17.9667 2.56667 17.325 2.56667 16.6667V7.50002C2.56667 7.24169 2.68332 7.00004 2.89166 6.84171L10.3917 1.00837C10.6917 0.775041 11.1167 0.775041 11.4167 1.00837L18.9167 6.84171C19.1167 7.00004 19.2417 7.24169 19.2417 7.50002V16.6667C19.2417 17.325 18.975 17.9667 18.5083 18.4333C18.0417 18.9 17.4 19.1667 16.7417 19.1667H5.06667ZM4.23334 7.90835V16.6667C4.23334 16.8834 4.32498 17.1 4.47498 17.2584C4.63332 17.4167 4.84167 17.5 5.06667 17.5H16.7333C16.95 17.5 17.1667 17.4084 17.325 17.2584C17.4833 17.1 17.5667 16.8917 17.5667 16.6667V7.90835L10.9 2.72504L4.23334 7.90835Z" fill="currentColor"/>
              <path d="M13.4 19.1667C12.9417 19.1667 12.5667 18.7917 12.5667 18.3334V10.8334H9.23334V18.3334C9.23334 18.7917 8.85834 19.1667 8.4 19.1667C7.94167 19.1667 7.56667 18.7917 7.56667 18.3334V10C7.56667 9.54171 7.94167 9.16671 8.4 9.16671H13.4C13.8583 9.16671 14.2333 9.54171 14.2333 10V18.3334C14.2333 18.7917 13.8583 19.1667 13.4 19.1667Z" fill="currentColor"/>
            </svg>
          }
          label="Home"
        />
        
        <NavItem
          icon={
            <svg width="20" height="20" viewBox="0 0 21 20" fill="none" className="w-5 h-5">
              <path d="M4.03334 19.1667C3.575 19.1667 3.2 18.7917 3.2 18.3334V2.50004C3.2 2.27504 3.29167 2.06674 3.44167 1.90841C3.62501 1.72507 4.65834 0.833374 7.36667 0.833374C8.775 0.833374 9.90833 1.28333 11.0083 1.725C12 2.125 12.9417 2.50004 14.0333 2.50004C16.0667 2.50004 16.7917 1.90006 16.8 1.89172C17.05 1.67506 17.4083 1.61669 17.7 1.75002C18 1.88335 18.2 2.17504 18.2 2.50004V12.5C18.2 12.725 18.1083 12.9333 17.9583 13.0917C17.775 13.275 16.7417 14.1667 14.0333 14.1667C12.625 14.1667 11.4917 13.7168 10.3917 13.2751C9.4 12.8751 8.45834 12.5 7.36667 12.5C6.00834 12.5 5.23334 12.7667 4.86667 12.9501V18.3334C4.86667 18.7917 4.49167 19.1667 4.03334 19.1667ZM7.36667 10.8334C8.775 10.8334 9.90833 11.2833 11.0083 11.725C12 12.125 12.9417 12.5 14.0333 12.5C15.3917 12.5 16.1667 12.2333 16.5333 12.05V3.84169C15.9333 4.02503 15.1167 4.16671 14.0333 4.16671C12.625 4.16671 11.4917 3.71675 10.3917 3.27509C9.4 2.87509 8.45834 2.50004 7.36667 2.50004C6.00834 2.50004 5.23334 2.76674 4.86667 2.95007V11.1584C5.46667 10.9751 6.28334 10.8334 7.36667 10.8334Z" fill="currentColor"/>
            </svg>
          }
          label="Fila"
        />
        
        <NavItem
          icon={
            <svg width="20" height="20" viewBox="0 0 21 20" fill="none" className="w-5 h-5">
              <path d="M10.5 19.1667C10.325 19.1667 10.15 19.1084 9.99999 19C9.71666 18.7834 3 13.6917 3 8.33337C3 6.33337 3.78332 4.45005 5.19999 3.03339C6.61665 1.61672 8.5 0.833374 10.5 0.833374C12.5 0.833374 14.3833 1.61672 15.8 3.03339C17.2167 4.45005 18 6.33337 18 8.33337C18 13.6917 11.2833 18.7834 11 19C10.85 19.1084 10.675 19.1667 10.5 19.1667ZM10.5 2.50004C8.94167 2.50004 7.47499 3.10836 6.37499 4.20836C5.27499 5.30836 4.66667 6.77504 4.66667 8.33337C4.66667 12.0917 9 16.025 10.5 17.2667C12 16.025 16.3333 12.0917 16.3333 8.33337C16.3333 6.77504 15.725 5.30836 14.625 4.20836C13.525 3.10836 12.0583 2.50004 10.5 2.50004Z" fill="currentColor"/>
              <path d="M10.5 11.6667C8.65833 11.6667 7.16667 10.175 7.16667 8.33337C7.16667 6.49171 8.65833 5.00004 10.5 5.00004C12.3417 5.00004 13.8333 6.49171 13.8333 8.33337C13.8333 10.175 12.3417 11.6667 10.5 11.6667ZM10.5 6.66671C9.58333 6.66671 8.83333 7.41671 8.83333 8.33337C8.83333 9.25004 9.58333 10 10.5 10C11.4167 10 12.1667 9.25004 12.1667 8.33337C12.1667 7.41671 11.4167 6.66671 10.5 6.66671Z" fill="currentColor"/>
            </svg>
          }
          label="Plantões"
        />
        
        <NavItem
          icon={
            <svg width="20" height="20" viewBox="0 0 21 20" fill="none" className="w-5 h-5">
              <path d="M17.8 8.33329H2.8M13.6333 1.66663V4.99996M6.96667 1.66663V4.99996M6.8 18.3333H13.8C15.2001 18.3333 15.9002 18.3333 16.435 18.0608C16.9054 17.8211 17.2878 17.4387 17.5275 16.9683C17.8 16.4335 17.8 15.7334 17.8 14.3333V7.33329C17.8 5.93316 17.8 5.2331 17.5275 4.69832C17.2878 4.22791 16.9054 3.84546 16.435 3.60578C15.9002 3.33329 15.2001 3.33329 13.8 3.33329H6.8C5.39987 3.33329 4.69981 3.33329 4.16503 3.60578C3.69462 3.84546 3.31217 4.22791 3.07249 4.69832C2.8 5.2331 2.8 5.93316 2.8 7.33329V14.3333C2.8 15.7334 2.8 16.4335 3.07249 16.9683C3.31217 17.4387 3.69462 17.8211 4.16503 18.0608C4.69981 18.3333 5.39987 18.3333 6.8 18.3333Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          }
          label="Agenda"
        />
        
        <NavItem
          icon={
            <svg width="20" height="20" viewBox="0 0 21 20" fill="none" className="w-5 h-5">
              <path d="M16.7667 15.8333H3.43334C2.97501 15.8333 2.60001 15.4583 2.60001 15C2.60001 14.5416 2.97501 14.1666 3.43334 14.1666H16.7667C17.225 14.1666 17.6 14.5416 17.6 15C17.6 15.4583 17.225 15.8333 16.7667 15.8333ZM16.7667 10.8333H3.43334C2.97501 10.8333 2.60001 10.4583 2.60001 9.99996C2.60001 9.54163 2.97501 9.16663 3.43334 9.16663H16.7667C17.225 9.16663 17.6 9.54163 17.6 9.99996C17.6 10.4583 17.225 10.8333 16.7667 10.8333ZM16.7667 5.83329H3.43334C2.97501 5.83329 2.60001 5.45829 2.60001 4.99996C2.60001 4.54163 2.97501 4.16663 3.43334 4.16663H16.7667C17.225 4.16663 17.6 4.54163 17.6 4.99996C17.6 5.45829 17.225 5.83329 16.7667 5.83329Z" fill="currentColor"/>
            </svg>
          }
          label="Menu"
        />
      </div>

      {/* Home Indicator */}
      <div className="flex justify-center py-2">
        <div className="w-[139px] h-1.5 bg-black rounded-full"></div>
      </div>
    </div>
  );
}
