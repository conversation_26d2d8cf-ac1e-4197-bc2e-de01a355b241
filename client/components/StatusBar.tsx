interface StatusBarProps {
  time: string;
}

export function StatusBar({ time }: StatusBarProps) {
  return (
    <div className="flex items-center justify-between px-5 py-3 h-11">
      {/* Time */}
      <div className="text-black text-sm font-semibold">
        {time}
      </div>
      
      {/* Signal and Battery Indicators */}
      <div className="flex items-center gap-1.5">
        {/* Signal bars */}
        <div className="flex items-end gap-0.5">
          <div className="w-1 h-1 bg-black rounded-full"></div>
          <div className="w-1 h-2 bg-black rounded-full"></div>
          <div className="w-1 h-3 bg-black rounded-full"></div>
          <div className="w-1 h-4 bg-black rounded-full"></div>
        </div>
        
        {/* WiFi icon */}
        <svg width="15" height="11" viewBox="0 0 68 12" fill="none" className="w-4 h-3">
          <path d="M28.1214 8.7311C29.397 7.65221 31.2658 7.65221 32.5414 8.7311C32.6055 8.7891 32.6431 8.87121 32.6449 8.95766C32.6466 9.04417 32.6119 9.12752 32.5501 9.18813L30.5531 11.2038C30.4945 11.263 30.4147 11.2965 30.3314 11.2965C30.2481 11.2965 30.1682 11.263 30.1097 11.2038L28.1117 9.18813C28.0502 9.12751 28.0161 9.04401 28.0179 8.95766C28.0198 8.87122 28.0573 8.78907 28.1214 8.7311ZM25.4564 6.04165C28.2045 3.48559 32.4602 3.48563 35.2083 6.04165C35.2703 6.10151 35.306 6.18403 35.307 6.27016C35.3079 6.35635 35.2739 6.43945 35.2132 6.50063L34.0589 7.66762C33.94 7.78672 33.7474 7.78935 33.6253 7.67348C32.7229 6.8563 31.5488 6.40391 30.3314 6.40395C29.1147 6.40445 27.9413 6.85681 27.0394 7.67348C26.9173 7.78929 26.7247 7.7867 26.6058 7.66762L25.4515 6.50063C25.3908 6.43956 25.3569 6.35627 25.3578 6.27016C25.3587 6.18402 25.3944 6.10149 25.4564 6.04165ZM22.7914 3.36C27.0063 -0.679043 33.6555 -0.679025 37.8705 3.36C37.9314 3.41996 37.9666 3.50205 37.9671 3.58754C37.9676 3.67311 37.9337 3.75534 37.8734 3.81606L36.7171 4.98305C36.598 5.10252 36.4054 5.10377 36.2845 4.98598C34.6786 3.45924 32.5472 2.60818 30.3314 2.60805C28.1153 2.60805 25.9834 3.45909 24.3773 4.98598C24.2565 5.10396 24.0637 5.10272 23.9447 4.98305L22.7874 3.81606C22.7274 3.75533 22.6941 3.67295 22.6947 3.58754C22.6953 3.50202 22.7303 3.41993 22.7914 3.36Z" fill="black"/>
        </svg>
        
        {/* Battery */}
        <div className="relative">
          <div className="w-6 h-3 border border-black rounded-sm opacity-35"></div>
          <div className="absolute inset-0.5 bg-black rounded-sm"></div>
          <div className="absolute -right-0.5 top-1 w-0.5 h-1 bg-black rounded-r opacity-40"></div>
        </div>
      </div>
    </div>
  );
}
