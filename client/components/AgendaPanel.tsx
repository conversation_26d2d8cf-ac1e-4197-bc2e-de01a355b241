import React from 'react';

interface AgendaPanelProps {
  onClose: () => void;
}

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
  linkText: string;
}

function MetricCard({ title, value, change, trend, linkText }: MetricCardProps) {
  const trendColor = trend === 'up' ? 'bg-feature-50' : 'bg-focus-50';
  const trendIcon = trend === 'up' ? (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className="w-5 h-5">
      <path d="M1.66877 15C1.4521 15 1.24375 14.9167 1.07708 14.7583C0.752082 14.4333 0.752082 13.9084 1.07708 13.5834L6.49375 8.16672C6.81875 7.84172 7.34378 7.84172 7.66878 8.16672L11.2438 11.7417L17.7354 5.25006C18.0604 4.92506 18.5854 4.92506 18.9104 5.25006C19.2354 5.57506 19.2354 6.09998 18.9104 6.42498L11.8271 13.5083C11.5021 13.8333 10.9771 13.8333 10.6521 13.5083L7.07709 9.93339L2.25211 14.7583C2.08544 14.925 1.87709 15 1.66043 15H1.66877Z" fill="#219C63"/>
      <path d="M18.3354 11.6667C17.877 11.6667 17.502 11.2917 17.502 10.8333V6.66667H13.3354C12.877 6.66667 12.502 6.29167 12.502 5.83333C12.502 5.375 12.877 5 13.3354 5H18.3354C18.7937 5 19.1687 5.375 19.1687 5.83333V10.8333C19.1687 11.2917 18.7937 11.6667 18.3354 11.6667Z" fill="#219C63"/>
    </svg>
  ) : (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className="w-5 h-5">
      <path d="M18.3271 14.9937C18.1104 14.9937 17.9021 14.9103 17.7354 14.752L11.2438 8.26041L7.66873 11.8353C7.34373 12.1603 6.81875 12.1603 6.49375 11.8353L1.07708 6.41868C0.752082 6.09368 0.752082 5.56875 1.07708 5.24375C1.40208 4.91875 1.92706 4.91875 2.25206 5.24375L7.07709 10.0687L10.6521 6.49375C10.9771 6.16875 11.5021 6.16875 11.8271 6.49375L18.9104 13.5771C19.2354 13.9021 19.2354 14.427 18.9104 14.752C18.7438 14.9187 18.5354 14.9937 18.3188 14.9937H18.3271Z" fill="#D83F20"/>
      <path d="M18.3271 14.9937L13.327 14.9937C12.8687 14.9937 12.4937 14.6187 12.4937 14.1604C12.4937 13.702 12.8687 13.327 13.327 13.327H17.4937V9.16036C17.4937 8.70203 17.8687 8.32703 18.327 8.32703C18.7854 8.32703 19.1604 8.70203 19.1604 9.16036V14.1604C19.1604 14.6187 18.7854 14.9937 18.3271 14.9937Z" fill="#D83F20"/>
    </svg>
  );

  return (
    <div className="flex-1 p-4 bg-white border border-surface-200 rounded-xl">
      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-bold text-baseline-primary leading-tight">{title}</h3>
          <div className="text-[34px] font-bold text-baseline-primary leading-none">{value}</div>
          <div className={`inline-flex items-center gap-2 px-2 py-1 rounded-lg ${trendColor}`}>
            {trendIcon}
            <span className="text-base font-bold text-greyscale-900">{change}</span>
          </div>
        </div>
        <div className="h-px bg-surface-200" />
        <div className="flex items-center gap-2">
          <span className="text-sm font-bold text-baseline-secondary">{linkText}</span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" className="w-5 h-5">
            <path d="M7.5021 15.8188C7.28543 15.8188 7.07708 15.7354 6.91041 15.5771C6.58541 15.2521 6.58541 14.7271 6.91041 14.4021L11.3188 9.99378L6.91041 5.58544C6.58541 5.26044 6.58541 4.73544 6.91041 4.41044C7.23541 4.08544 7.76044 4.08544 8.08544 4.41044L13.0854 9.41044C13.4104 9.73544 13.4104 10.2604 13.0854 10.5854L8.08544 15.5854C7.91878 15.7521 7.71042 15.8271 7.49376 15.8271L7.5021 15.8188Z" fill="#3E7998"/>
          </svg>
        </div>
      </div>
    </div>
  );
}

export function AgendaPanel({ onClose }: AgendaPanelProps) {
  return (
    <div className="px-4">
      <div className="bg-white rounded-2xl border border-surface-200 shadow-sm">
        <div className="flex items-center justify-between p-6 pb-4">
          <div>
            <h2 className="text-lg font-bold text-primary-900 tracking-tight">Plantões em tempo real</h2>
            <p className="text-xs text-primary-900 mt-1">Última atualização: 08:26</p>
          </div>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-full transition-colors" aria-label="Fechar agenda">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
              <path d="M18 6L6 18M6 6L18 18" stroke="#203C47" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        <div className="px-6 pb-4">
          <div className="flex gap-2">
            <div className="flex items-center gap-2 px-4 py-2 bg-primary-50 rounded-xl flex-1">
              <span className="text-baseline-primary font-normal text-sm">Brasil</span>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
                <path d="M12.0025 15.9925C11.7425 15.9925 11.4925 15.8925 11.2925 15.7025L5.2925 9.7025C4.9025 9.3125 4.9025 8.6825 5.2925 8.2925C5.6825 7.9025 6.3125 7.9025 6.7025 8.2925L11.9925 13.5825L17.2825 8.2925C17.6725 7.9025 18.3025 7.9025 18.6925 8.2925C19.0825 8.6825 19.0825 9.3125 18.6925 9.7025L12.6925 15.7025C12.4925 15.9025 12.2425 15.9925 11.9825 15.9925H12.0025Z" fill="#203C47"/>
              </svg>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-primary-50 rounded-xl flex-1">
              <span className="text-baseline-primary font-normal text-sm">Todos os plantões</span>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="w-6 h-6">
                <path d="M12.0025 15.9925C11.7425 15.9925 11.4925 15.8925 11.2925 15.7025L5.2925 9.7025C4.9025 9.3125 4.9025 8.6825 5.2925 8.2925C5.6825 7.9025 6.3125 7.9025 6.7025 8.2925L11.9925 13.5825L17.2825 8.2925C17.6725 7.9025 18.3025 7.9025 18.6925 8.2925C19.0825 8.6825 19.0825 9.3125 18.6925 9.7025L12.6925 15.7025C12.4925 15.9025 12.2425 15.9925 11.9825 15.9925H12.0025Z" fill="#203C47"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="px-6 pb-6 space-y-2">
          <div className="flex gap-2">
            <MetricCard title="Plantões com algum corretor" value="19" change="+15%" trend="up" linkText="Ver mais" />
            <MetricCard title="Corretores nos plantões" value="677" change="+15%" trend="up" linkText="Ver mais" />
          </div>
          <div className="flex gap-2">
            <MetricCard title="Atendimentos totais do dia" value="17" change="-9%" trend="down" linkText="Ver mais" />
            <MetricCard title="Clientes sendo atendidos agora" value="7" change="+15%" trend="up" linkText="Ver mais" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default AgendaPanel;
