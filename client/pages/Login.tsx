import { useState } from "react";
import { StatusBar } from "@/components/StatusBar";
import { LoginForm } from "@/components/login/LoginForm";
import { CompanyCodeForm } from "@/components/login/CompanyCodeForm";
import { CreateAccountForm } from "@/components/login/CreateAccountForm";
import { CompanySelectModal } from "@/components/login/CompanySelectModal";

export type LoginStep = 'login' | 'companyCode' | 'createAccount';

export interface Company {
  id: string;
  name: string;
  logo?: string;
}

const COMPANIES: Company[] = [
  { id: 'tecnisa', name: '<PERSON><PERSON><PERSON><PERSON>', logo: 'https://api.builder.io/api/v1/image/assets/TEMP/5756501bb205036eac052e77a42aa3b0e9795842?width=64' },
  { id: 'cury', name: '<PERSON>ury' },
  { id: 'outra', name: '<PERSON>ra' },
  { id: 'custom', name: 'No<PERSON> da Empresa', logo: 'https://api.builder.io/api/v1/image/assets/TEMP/5756501bb205036eac052e77a42aa3b0e9795842?width=64' },
];

export default function Login() {
  const [currentStep, setCurrentStep] = useState<LoginStep>('login');
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [showCompanyModal, setShowCompanyModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState("6:43");

  const handleContinue = async (email: string) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
    setCurrentStep('companyCode');
  };

  const handleCompanyCodeSubmit = async (code: string) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
    
    // Auto-select a company based on code (fake logic)
    const company = COMPANIES[0]; // Tecnisa
    setSelectedCompany(company);
    setCurrentStep('createAccount');
  };

  const handleCreateAccount = async (userData: any) => {
    setIsLoading(true);
    // Simulate account creation
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
    // Navigate to main app or show success
    console.log('Account created:', userData);
  };

  const handleCompanySelect = (company: Company) => {
    setSelectedCompany(company);
    setShowCompanyModal(false);
  };

  const handleGoBack = () => {
    if (currentStep === 'companyCode') {
      setCurrentStep('login');
    } else if (currentStep === 'createAccount') {
      setCurrentStep('companyCode');
    }
  };

  return (
    <div className="min-h-screen bg-white font-dm-sans">
      <div className="max-w-sm mx-auto bg-white shadow-lg min-h-screen relative">
        <StatusBar time={currentTime} />
        
        {currentStep === 'login' && (
          <LoginForm 
            onContinue={handleContinue}
            onCreateAccount={() => setCurrentStep('createAccount')}
            isLoading={isLoading}
          />
        )}
        
        {currentStep === 'companyCode' && (
          <CompanyCodeForm 
            onSubmit={handleCompanyCodeSubmit}
            onGoBack={handleGoBack}
            isLoading={isLoading}
          />
        )}
        
        {currentStep === 'createAccount' && (
          <CreateAccountForm 
            selectedCompany={selectedCompany}
            onSubmit={handleCreateAccount}
            onGoBack={handleGoBack}
            onSelectCompany={() => setShowCompanyModal(true)}
            isLoading={isLoading}
          />
        )}

        {showCompanyModal && (
          <CompanySelectModal
            companies={COMPANIES}
            onSelect={handleCompanySelect}
            onClose={() => setShowCompanyModal(false)}
          />
        )}
      </div>
    </div>
  );
}
