import { BottomNavigation } from "@/components/BottomNavigation";
import { CheckInSection } from "@/components/CheckInSection";
import { StatusBar } from "@/components/StatusBar";
import { useEffect, useState } from "react";

export default function Index() {
  const [currentTime, setCurrentTime] = useState("6:43");

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      setCurrentTime(`${hours}:${minutes}`);
    };

    updateTime();
    const interval = setInterval(updateTime, 60000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-white font-dm-sans">
      {/* Mobile Container */}
      <div className="max-w-sm mx-auto bg-white shadow-lg">
        {/* Status Bar */}
        <StatusBar time={currentTime} />

        {/* Check-in Section */}
        <CheckInSection />

        {/* Main Content */}
        <div className="flex flex-col">
          {/* Greeting */}
          <div className="px-4 pt-6 pb-4">
            <h1 className="text-lg font-bold text-primary-900 tracking-tight">
              Olá, Júlio
            </h1>
          </div>

          {/* Blue Notification Card */}
          <div className="px-4 pb-4">
            <div className="bg-[#B8D4E0] rounded-2xl p-4 relative">
              {/* Check icon */}
              <div className="absolute top-4 right-4">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M13.3334 4L6.00008 11.3333L2.66675 8" stroke="#219C63" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
              </div>

              <div className="pr-12">
                <p className="text-sm font-medium text-[#203C47] leading-relaxed">
                  Não há nenhuma pendência, que tal estudar o último lançamento?
                </p>
              </div>

              {/* Location Card */}
              <div className="mt-4 bg-white rounded-xl p-3 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg overflow-hidden">
                    <img
                      src="/placeholder.svg"
                      alt="Cidade Lapa"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-bold text-sm text-[#203C47]">Cidade Lapa - Água Branca</h3>
                  </div>
                </div>
                <div className="w-8 h-8 bg-[#203C47] rounded-full flex items-center justify-center">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M6 12L10 8L6 4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Próximos atendimentos */}
          <div className="px-4 pb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-bold text-primary-900">Próximos atendimentos</h2>
              <button className="flex items-center gap-2">
                <span className="text-sm font-bold text-baseline-secondary">Ver agenda</span>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M6 12L10 8L6 4" stroke="#3E7998" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </button>
            </div>

            {/* No appointments card */}
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 text-[#4197C5]">
                <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                  <path d="M53.3333 21.3333H10.6667M42.6667 5.33333V16M21.3333 5.33333V16M21.3333 58.6667H42.6667C48.1895 58.6667 50.9509 58.6667 52.8921 57.5439C54.5772 56.5748 55.9085 55.2435 56.8776 53.5584C58 51.6172 58 48.8558 58 43.3333V21.3333C58 15.8105 58 13.0491 56.8776 11.1079C55.9085 9.42281 54.5772 8.09148 52.8921 7.12237C50.9509 6 48.1895 6 42.6667 6H21.3333C15.8105 6 13.0491 6 11.1079 7.12237C9.42281 8.09148 8.09148 9.42281 7.12237 11.1079C6 13.0491 6 15.8105 6 21.3333V43.3333C6 48.8558 6 51.6172 7.12237 53.5584C8.09148 55.2435 9.42281 56.5748 11.1079 57.5439C13.0491 58.6667 15.8105 58.6667 21.3333 58.6667Z" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M37.3333 37.3333L42.6667 32L37.3333 26.6667M26.6667 26.6667L21.3333 32L26.6667 37.3333" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <h3 className="font-bold text-lg text-primary-900 mb-2">Não há atendimentos</h3>
              <p className="text-sm text-gray-500 mb-6">[Mensagem]</p>
              <button className="w-full bg-baseline-primary text-white font-bold py-3 px-4 rounded-2xl">
                Agendar atendimento
              </button>
            </div>
          </div>

          {/* Plantões próximos */}
          <div className="px-4 pb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-bold text-primary-900">Plantões próximos</h2>
              <button className="flex items-center gap-2">
                <span className="text-sm font-bold text-baseline-secondary">Ver todos</span>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M6 12L10 8L6 4" stroke="#3E7998" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </button>
            </div>

            {/* Location permission card */}
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 text-[#4197C5]">
                <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                  <path d="M32 61.3333C31.3 61.3333 30.6 61.0833 30.0667 60.6C29.0667 59.7333 8 42.2 8 21.3333C8 16.0333 10.1067 11.0667 13.8667 7.30667C17.6267 3.54667 22.6 1.33333 28 1.33333C33.4 1.33333 38.3733 3.54667 42.1333 7.30667C45.8933 11.0667 48 16.0333 48 21.3333C48 42.2 26.9333 59.7333 26 60.6C25.4667 61.0833 24.7667 61.3333 24.0667 61.3333H32ZM32 6.66667C24.6667 6.66667 18.6667 12.6667 18.6667 21.3333C18.6667 35.4667 30.6667 48.6667 32 50.1333C33.3333 48.6667 45.3333 35.4667 45.3333 21.3333C45.3333 12.6667 39.3333 6.66667 32 6.66667Z" fill="currentColor" />
                  <path d="M32 29.3333C27.2 29.3333 23.3333 25.4667 23.3333 20.6667C23.3333 15.8667 27.2 12 32 12C36.8 12 40.6667 15.8667 40.6667 20.6667C40.6667 25.4667 36.8 29.3333 32 29.3333ZM32 17.3333C30.2667 17.3333 28.6667 18.9333 28.6667 20.6667C28.6667 22.4 30.2667 24 32 24C33.7333 24 35.3333 22.4 35.3333 20.6667C35.3333 18.9333 33.7333 17.3333 32 17.3333Z" fill="currentColor" />
                  <path d="M45.3333 45.3333L18.6667 18.6667" stroke="currentColor" strokeWidth="4" strokeLinecap="round" />
                </svg>
              </div>
              <h3 className="font-bold text-lg text-primary-900 mb-2">Habilite a localização de seu celular</h3>
              <p className="text-sm text-gray-500 mb-6">[Mensagem]</p>
              <button className="w-full bg-baseline-primary text-white font-bold py-3 px-4 rounded-2xl">
                Habilitar localização
              </button>
            </div>
          </div>

          {/* Bottom spacing for navigation */}
          <div className="h-24"></div>
        </div>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </div>
    </div>
  );
}
