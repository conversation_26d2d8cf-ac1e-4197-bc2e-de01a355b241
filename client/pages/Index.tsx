import { useState, useEffect } from "react";
import { StatusBar } from "@/components/StatusBar";
import { CheckInSection } from "@/components/CheckInSection";
import { NotificationCard } from "@/components/NotificationCard";
import { UpcomingAppointments } from "@/components/UpcomingAppointments";
import { PerformanceChart } from "@/components/PerformanceChart";
import { NearbyLocations } from "@/components/NearbyLocations";
import { BottomNavigation } from "@/components/BottomNavigation";

export default function Index() {
  const [currentTime, setCurrentTime] = useState("6:43");

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      setCurrentTime(`${hours}:${minutes}`);
    };

    updateTime();
    const interval = setInterval(updateTime, 60000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-white font-dm-sans">
      {/* Mobile Container */}
      <div className="max-w-sm mx-auto bg-white shadow-lg">
        {/* Status Bar */}
        <StatusBar time={currentTime} />
        
        {/* Check-in Section */}
        <CheckInSection />

        {/* Main Content */}
        <div className="flex flex-col gap-2">
          {/* Greeting */}
          <div className="px-4 py-6">
            <h1 className="text-lg font-bold text-primary-900 tracking-tight">
              Olá, Júlio
            </h1>
          </div>

          {/* Notification Cards */}
          <div className="px-4 space-y-2">
            <NotificationCard
              type="warning"
              message="Você possui um atendimento em"
              highlight="53 minutos"
            />
            <NotificationCard
              type="warning"
              message="Checkout obrigatório em"
              highlight="12 minutos"
            />
          </div>


          {/* Upcoming Appointments */}
          <UpcomingAppointments />

          {/* Performance Chart */}
          <PerformanceChart />

          {/* Nearby Locations */}
          <NearbyLocations />

          {/* Bottom spacing for navigation */}
          <div className="h-24"></div>
        </div>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </div>
    </div>
  );
}
